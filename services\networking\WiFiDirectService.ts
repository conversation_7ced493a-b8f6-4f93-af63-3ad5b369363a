import { Platform, PermissionsAndroid } from 'react-native';
import { SingletonService } from '../base/BaseService';

// Mock WiFi P2P module for development - will be replaced with actual implementation
const WiFiP2P = {
  initialize: () => Promise.resolve(),
  isSuccessfulInitialization: () => Promise.resolve(true),
  startPeerDiscovery: () => Promise.resolve(),
  stopPeerDiscovery: () => Promise.resolve(),
  getAvailablePeers: () => Promise.resolve({ devices: [] }),
  connect: () => Promise.resolve(),
  disconnect: () => Promise.resolve(),
  createGroup: () => Promise.resolve(),
  removeGroup: () => Promise.resolve(),
  getConnectionInfo: () => Promise.resolve({}),
  getGroupInfo: () => Promise.resolve(null),
  sendMessage: () => Promise.resolve(),
  receiveMessage: () => Promise.resolve(),
};

export interface WiFiDirectPeer {
  deviceAddress: string;
  deviceName: string;
  primaryDeviceType?: string;
  secondaryDeviceType?: string;
  isGroupOwner?: boolean;
  status?: number;
}

export interface WiFiDirectGroup {
  interface: string;
  groupOwnerAddress: string;
  isGroupOwner: boolean;
  clients: WiFiDirectPeer[];
}

export interface WiFiDirectServiceInterface {
  initialize(): Promise<boolean>;
  isAvailable(): boolean;
  requestPermissions(): Promise<boolean>;
  startPeerDiscovery(): Promise<void>;
  stopPeerDiscovery(): Promise<void>;
  getAvailablePeers(): Promise<WiFiDirectPeer[]>;
  connectToPeer(peerAddress: string): Promise<boolean>;
  disconnect(): Promise<boolean>;
  createGroup(): Promise<boolean>;
  removeGroup(): Promise<boolean>;
  getConnectionInfo(): Promise<any>;
  getGroupInfo(): Promise<WiFiDirectGroup | null>;
  sendData(data: string): Promise<boolean>;
  onPeersChanged(callback: (peers: WiFiDirectPeer[]) => void): void;
  onConnectionChanged(callback: (connectionInfo: any) => void): void;
  onDataReceived(callback: (data: string) => void): void;
}

class WiFiDirectService extends SingletonService implements WiFiDirectServiceInterface {
  private isDiscovering = false;
  private availablePeers: WiFiDirectPeer[] = [];
  private hasPermissions = false;

  // Event callbacks for backward compatibility
  private peersChangedCallback?: (peers: WiFiDirectPeer[]) => void;
  private connectionChangedCallback?: (connectionInfo: any) => void;
  private dataReceivedCallback?: (data: string) => void;

  constructor() {
    super('WiFiDirectService');
  }

  static getInstance(): WiFiDirectService {
    return super.getInstance.call(this, 'WiFiDirectService');
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('[WiFiDirectService] Initializing...');

      if (Platform.OS !== 'android') {
        console.warn('[WiFiDirectService] WiFi Direct is only available on Android');
        return false;
      }

      // Request permissions
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        console.error('[WiFiDirectService] Failed to get WiFi Direct permissions');
        return false;
      }

      // Initialize WiFi P2P
      await WiFiP2P.initialize();
      const initSuccess = await WiFiP2P.isSuccessfulInitialization();

      if (!initSuccess) {
        console.error('[WiFiDirectService] WiFi P2P initialization failed');
        return false;
      }

      // Setup event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      this.emitStatusChange('initialized');
      console.log('[WiFiDirectService] Initialized successfully');
      return true;
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error(String(error)), 'initialize');
      return false;
    }
  }

  private setupEventListeners(): void {
    try {
      // Set up WiFi Direct event listeners
      // Note: react-native-wifi-p2p uses DeviceEventEmitter for events
      const { DeviceEventEmitter } = require('react-native');

      DeviceEventEmitter.addListener('PEERS_UPDATED', (event: any) => {
        this.availablePeers = event.devices || [];
        this.emit('peersChanged', this.availablePeers);
        this.peersChangedCallback?.(this.availablePeers);
        console.log(`[WiFiDirectService] Peers updated: ${this.availablePeers.length} peers`);
      });

      DeviceEventEmitter.addListener('CONNECTION_INFO_UPDATED', (event: any) => {
        this.emit('connectionChanged', event);
        this.connectionChangedCallback?.(event);
        console.log('[WiFiDirectService] Connection info updated');
      });

      DeviceEventEmitter.addListener('MESSAGE_RECEIVED', (event: any) => {
        this.emit('dataReceived', event.message);
        this.dataReceivedCallback?.(event.message);
        console.log('[WiFiDirectService] Message received');
      });
    } catch (error) {
      this.emitError(
        error instanceof Error ? error : new Error(String(error)),
        'setupEventListeners',
      );
    }
  }

  isAvailable(): boolean {
    return Platform.OS === 'android' && this.isInitialized;
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      this.hasPermissions = false;
      return false;
    }

    try {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_WIFI_STATE,
        PermissionsAndroid.PERMISSIONS.CHANGE_WIFI_STATE,
      ];

      // For Android 13+ (API 33+)
      if (Platform.Version >= 33) {
        permissions.push(PermissionsAndroid.PERMISSIONS.NEARBY_WIFI_DEVICES);
      }

      const granted = await PermissionsAndroid.requestMultiple(permissions);

      this.hasPermissions = Object.values(granted).every(
        permission => permission === PermissionsAndroid.RESULTS.GRANTED,
      );
      return this.hasPermissions;
    } catch (error) {
      console.error('[WiFiDirectService] Error requesting permissions:', error);
      this.hasPermissions = false;
      return false;
    }
  }

  async startPeerDiscovery(): Promise<void> {
    if (!this.isAvailable()) {
      throw new Error('WiFi Direct is not available');
    }

    try {
      if (this.isDiscovering) {
        await this.stopPeerDiscovery();
      }

      await startPeerDiscovery();
      this.isDiscovering = true;
    } catch (error) {
      console.error('Error starting peer discovery:', error);
      throw error;
    }
  }

  async stopPeerDiscovery(): Promise<void> {
    if (!this.isAvailable()) {
      return;
    }

    try {
      if (this.isDiscovering) {
        await stopPeerDiscovery();
        this.isDiscovering = false;
      }
    } catch (error) {
      console.error('Error stopping peer discovery:', error);
      throw error;
    }
  }

  async getAvailablePeers(): Promise<WiFiDirectPeer[]> {
    if (!this.isAvailable()) {
      return [];
    }

    try {
      const peers = await getCurrentPeers();
      this.availablePeers = peers || [];
      return this.availablePeers;
    } catch (error) {
      console.error('Error getting available peers:', error);
      return [];
    }
  }

  async connectToPeer(peerAddress: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await connect(peerAddress);
      return true;
    } catch (error) {
      console.error('Error connecting to peer:', error);
      return false;
    }
  }

  async disconnect(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await disconnect();
      return true;
    } catch (error) {
      console.error('Error disconnecting:', error);
      return false;
    }
  }

  async createGroup(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await createGroup();
      return true;
    } catch (error) {
      console.error('Error creating group:', error);
      return false;
    }
  }

  async removeGroup(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await removeGroup();
      return true;
    } catch (error) {
      console.error('Error removing group:', error);
      return false;
    }
  }

  async getConnectionInfo(): Promise<any> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      return await getConnectionInfo();
    } catch (error) {
      console.error('Error getting connection info:', error);
      return null;
    }
  }

  async getGroupInfo(): Promise<WiFiDirectGroup | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      return await getGroupInfo();
    } catch (error) {
      console.error('Error getting group info:', error);
      return null;
    }
  }

  async sendData(data: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await sendMessage(data);
      return true;
    } catch (error) {
      console.error('Error sending data:', error);
      return false;
    }
  }

  onPeersChanged(callback: (peers: WiFiDirectPeer[]) => void): void {
    this.peersChangedCallback = callback;
  }

  onConnectionChanged(callback: (connectionInfo: any) => void): void {
    this.connectionChangedCallback = callback;
  }

  onDataReceived(callback: (data: string) => void): void {
    this.dataReceivedCallback = callback;
  }
}

export default new WiFiDirectService();
