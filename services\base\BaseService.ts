import { EventEmitter } from 'events';

/**
 * Base service class that provides common functionality for all services
 * Uses React Native compatible EventEmitter pattern
 */
export abstract class BaseService extends EventEmitter {
  protected isInitialized: boolean = false;
  protected isStarted: boolean = false;
  protected serviceName: string;

  constructor(serviceName: string) {
    super();
    this.serviceName = serviceName;
    this.setMaxListeners(50); // Increase listener limit for complex services
  }

  /**
   * Initialize the service
   */
  abstract initialize(): Promise<boolean>;

  /**
   * Start the service
   */
  async start(): Promise<boolean> {
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        return false;
      }
    }

    this.isStarted = true;
    this.emit('started');
    return true;
  }

  /**
   * Stop the service
   */
  async stop(): Promise<void> {
    this.isStarted = false;
    this.emit('stopped');
  }

  /**
   * Get service status
   */
  getStatus(): { initialized: boolean; started: boolean; name: string } {
    return {
      initialized: this.isInitialized,
      started: this.isStarted,
      name: this.serviceName,
    };
  }

  /**
   * Emit error with proper error handling
   */
  protected emitError(error: Error | string, context?: string): void {
    const errorObj = error instanceof Error ? error : new Error(error);
    console.error(`[${this.serviceName}] Error${context ? ` in ${context}` : ''}:`, errorObj);
    this.emit('error', errorObj, context);
  }

  /**
   * Emit status change
   */
  protected emitStatusChange(status: string, data?: any): void {
    console.log(`[${this.serviceName}] Status: ${status}`);
    this.emit('statusChange', status, data);
  }

  /**
   * Safe async operation wrapper
   */
  protected async safeAsync<T>(
    operation: () => Promise<T>,
    context: string,
    fallback?: T,
  ): Promise<T | undefined> {
    try {
      return await operation();
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error(String(error)), context);
      return fallback;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.stop();
    this.removeAllListeners();
  }
}

/**
 * Singleton base class for services that should have only one instance
 */
export abstract class SingletonService extends BaseService {
  private static instances: Map<string, any> = new Map();

  constructor(serviceName: string) {
    super(serviceName);
  }

  /**
   * Get singleton instance
   */
  protected static getInstance<T extends SingletonService>(
    this: new () => T,
    serviceName: string,
  ): T {
    if (!SingletonService.instances.has(serviceName)) {
      SingletonService.instances.set(serviceName, new this());
    }
    return SingletonService.instances.get(serviceName) as T;
  }

  /**
   * Clear singleton instance (for testing)
   */
  protected static clearInstance(serviceName: string): void {
    const instance = SingletonService.instances.get(serviceName);
    if (instance) {
      instance.cleanup();
      SingletonService.instances.delete(serviceName);
    }
  }
}

/**
 * Service manager for handling multiple services
 */
export class ServiceManager {
  private services: Map<string, BaseService> = new Map();
  private startupOrder: string[] = [];

  /**
   * Register a service
   */
  register(service: BaseService): void {
    const status = service.getStatus();
    this.services.set(status.name, service);
  }

  /**
   * Set startup order for services
   */
  setStartupOrder(order: string[]): void {
    this.startupOrder = order;
  }

  /**
   * Start all services in order
   */
  async startAll(): Promise<boolean> {
    const servicesToStart =
      this.startupOrder.length > 0 ? this.startupOrder : Array.from(this.services.keys());

    for (const serviceName of servicesToStart) {
      const service = this.services.get(serviceName);
      if (service) {
        console.log(`Starting service: ${serviceName}`);
        const started = await service.start();
        if (!started) {
          console.error(`Failed to start service: ${serviceName}`);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Stop all services
   */
  async stopAll(): Promise<void> {
    const servicesToStop = [...this.services.values()].reverse();

    for (const service of servicesToStop) {
      await service.stop();
    }
  }

  /**
   * Get service by name
   */
  getService<T extends BaseService>(name: string): T | undefined {
    return this.services.get(name) as T;
  }

  /**
   * Get all service statuses
   */
  getAllStatuses(): Array<{ initialized: boolean; started: boolean; name: string }> {
    return Array.from(this.services.values()).map(service => service.getStatus());
  }
}
