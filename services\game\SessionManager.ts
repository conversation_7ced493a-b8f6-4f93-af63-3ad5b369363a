import { GameInfo } from './GameDetector';
import { CompatibilityCheck } from './GameCompatibility';
import { SingletonService } from '../base/BaseService';

export interface GameSession {
  id: string;
  name: string;
  game: GameInfo;
  host: Player;
  players: Player[];
  maxPlayers: number;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'ended';
  settings: SessionSettings;
  createdAt: number;
  startedAt?: number;
  endedAt?: number;
  networkType: 'wifi' | 'bluetooth' | 'local';
  connectionInfo: ConnectionInfo;
  compatibility?: CompatibilityCheck;
}

export interface Player {
  id: string;
  name: string;
  deviceType: 'ios' | 'android';
  gameVersion: string;
  isHost: boolean;
  isReady: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  lastSeen: number;
  avatar?: string;
  stats?: PlayerStats;
}

export interface PlayerStats {
  gamesPlayed: number;
  gamesWon: number;
  totalPlaytime: number;
  favoriteGames: string[];
}

export interface SessionSettings {
  isPrivate: boolean;
  password?: string;
  allowSpectators: boolean;
  maxSpectators: number;
  autoStart: boolean;
  gameMode?: string;
  difficulty?: string;
  customRules?: { [key: string]: any };
}

export interface ConnectionInfo {
  hostIP?: string;
  port?: number;
  bluetoothId?: string;
  networkName?: string;
  securityToken: string;
}

export interface SessionInvite {
  sessionId: string;
  sessionName: string;
  hostName: string;
  gameName: string;
  playerCount: number;
  maxPlayers: number;
  expiresAt: number;
  connectionData: string;
}

class SessionManager extends SingletonService {
  private activeSessions: Map<string, GameSession> = new Map();
  private currentSession: GameSession | null = null;
  private sessionHistory: GameSession[] = [];

  constructor() {
    super('SessionManager');
  }

  static getInstance(): SessionManager {
    return super.getInstance.call(this, 'SessionManager');
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('[SessionManager] Initializing...');

      // Initialize session management
      this.activeSessions.clear();
      this.currentSession = null;

      this.isInitialized = true;
      this.emitStatusChange('initialized');
      console.log('[SessionManager] Initialized successfully');
      return true;
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error(String(error)), 'initialize');
      return false;
    }
  }

  async createSession(
    game: GameInfo,
    host: Player,
    settings: Partial<SessionSettings> = {},
  ): Promise<GameSession> {
    try {
      // Enhanced validation and setup
      await this.validateSessionCreation(game, host, settings);

      const sessionId = this.generateSessionId();
      const sessionCode = this.generateSessionCode();
      const networkConfig = await this.setupNetworkConfiguration();

      const defaultSettings: SessionSettings = {
        isPrivate: false,
        allowSpectators: true,
        maxSpectators: 10,
        autoStart: false,
        ...settings,
      };

      const session: GameSession = {
        id: sessionId,
        name: `${host.name}'s ${game.name} Session`,
        game,
        host: {
          ...host,
          isHost: true,
          isReady: true,
          connectionStatus: 'connected',
          lastSeen: Date.now(),
          gameVersion: game.version || '1.0.0',
        },
        players: [
          {
            ...host,
            isHost: true,
            isReady: true,
            connectionStatus: 'connected',
            lastSeen: Date.now(),
            gameVersion: game.version || '1.0.0',
          },
        ],
        maxPlayers: game.requirements?.maxPlayers || 8,
        status: 'waiting',
        settings: defaultSettings,
        createdAt: Date.now(),
        networkType: 'wifi', // Default, can be changed
        connectionInfo: {
          securityToken: this.generateSecurityToken(),
          hostIP: networkConfig.hostAddress,
          port: networkConfig.port,
          networkName: networkConfig.networkName,
        },
        sessionCode,
        qrCode: await this.generateSessionQRCode(sessionId, networkConfig),
        metadata: {
          gameVersion: game.version || '1.0.0',
          protocolVersion: '1.0',
          features: game.features || [],
          requirements: game.requirements,
          encryption: true,
        },
      };

      // Store session
      this.activeSessions.set(sessionId, session);
      this.currentSession = session;

      // Start session monitoring
      this.startSessionMonitoring(sessionId);

      this.emit('sessionCreated', session);
      console.log(`Enhanced session created: ${sessionId} with code: ${sessionCode}`);

      return session;
    } catch (error) {
      console.error('Failed to create session:', error);
      throw error;
    }
  }

  private async validateSessionCreation(
    game: GameInfo,
    host: Player,
    settings: Partial<SessionSettings>,
  ): Promise<void> {
    // Validate game supports multiplayer
    if (!game.supportsMultiplayer) {
      throw new Error('Game does not support multiplayer');
    }

    // Validate host information
    if (!host.id || !host.name) {
      throw new Error('Invalid host information');
    }

    // Validate player count limits
    const maxPlayers = settings.maxSpectators || game.requirements?.maxPlayers || 8;
    if (maxPlayers < 1 || maxPlayers > 16) {
      throw new Error('Invalid player count limits');
    }

    // Check if host already has an active session
    const existingSession = Array.from(this.activeSessions.values()).find(
      session => session.host.id === host.id && session.status !== 'ended',
    );

    if (existingSession) {
      throw new Error('Host already has an active session');
    }
  }

  private async setupNetworkConfiguration(): Promise<{
    hostAddress: string;
    port: number;
    networkName: string;
  }> {
    try {
      // Get local network information
      const hostAddress = await this.getLocalIPAddress();
      const port = await this.findAvailablePort(8080);
      const networkName = await this.getNetworkName();

      return {
        hostAddress,
        port,
        networkName,
      };
    } catch (error) {
      console.error('Failed to setup network configuration:', error);
      return {
        hostAddress: '*************',
        port: 8080,
        networkName: 'Local Network',
      };
    }
  }

  private async getLocalIPAddress(): Promise<string> {
    try {
      // Simulate getting local IP address
      const localIPs = ['192.168.1.', '192.168.0.', '10.0.0.', '172.16.0.'];
      const randomBase = localIPs[Math.floor(Math.random() * localIPs.length)];
      const randomEnd = Math.floor(Math.random() * 254) + 2;
      return `${randomBase}${randomEnd}`;
    } catch (error) {
      return '127.0.0.1';
    }
  }

  private async findAvailablePort(preferredPort: number): Promise<number> {
    try {
      // Simulate port availability check
      const isAvailable = Math.random() > 0.3; // 70% chance preferred port is available

      if (isAvailable) {
        return preferredPort;
      } else {
        // Return a random port in the dynamic range
        return Math.floor(Math.random() * (65535 - 49152)) + 49152;
      }
    } catch (error) {
      return 8080;
    }
  }

  private async getNetworkName(): Promise<string> {
    try {
      // Simulate getting network name
      const networkNames = [
        'Home WiFi',
        'Office Network',
        'Mobile Hotspot',
        'Guest Network',
        'Gaming Network',
      ];
      return networkNames[Math.floor(Math.random() * networkNames.length)];
    } catch (error) {
      return 'Unknown Network';
    }
  }

  private generateSessionCode(): string {
    // Generate a 6-character alphanumeric code
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private async generateSessionQRCode(sessionId: string, networkConfig: any): Promise<string> {
    try {
      // Generate QR code data with session information
      const qrData = {
        type: 'logaco-session',
        sessionId,
        hostAddress: networkConfig.hostAddress,
        port: networkConfig.port,
        networkName: networkConfig.networkName,
        timestamp: Date.now(),
        version: '1.0',
      };

      // In real implementation, this would generate an actual QR code
      // For now, return base64 encoded JSON
      return btoa(JSON.stringify(qrData));
    } catch (error) {
      console.error('Failed to generate QR code:', error);
      return '';
    }
  }

  private startSessionMonitoring(sessionId: string): void {
    // Start monitoring session health
    const monitoringInterval = setInterval(() => {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        clearInterval(monitoringInterval);
        return;
      }

      // Check session timeout (30 minutes for waiting sessions)
      const now = Date.now();
      const sessionAge = now - session.createdAt;
      const timeout = 30 * 60 * 1000; // 30 minutes

      if (sessionAge > timeout && session.status === 'waiting') {
        console.log(`Session ${sessionId} timed out`);
        this.endSession(sessionId);
        clearInterval(monitoringInterval);
      }

      // Update player last seen times
      session.players.forEach(player => {
        if (player.connectionStatus === 'connected') {
          player.lastSeen = now;
        }
      });
    }, 30000); // Check every 30 seconds

    // Store interval reference for cleanup
    this.sessionMonitors.set(sessionId, monitoringInterval);
  }

  private sessionMonitors = new Map<string, NodeJS.Timeout>();

  async joinSession(sessionId: string, player: Player): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.status !== 'waiting') {
      throw new Error('Session is not accepting new players');
    }

    if (session.players.length >= session.maxPlayers) {
      throw new Error('Session is full');
    }

    // Check if player is already in session
    const existingPlayer = session.players.find(p => p.id === player.id);
    if (existingPlayer) {
      throw new Error('Player already in session');
    }

    // Add player to session
    const newPlayer: Player = {
      ...player,
      isHost: false,
      isReady: false,
      connectionStatus: 'connected',
      lastSeen: Date.now(),
    };

    session.players.push(newPlayer);
    this.currentSession = session;

    this.emit('playerJoined', { session, player: newPlayer });

    // Check if auto-start is enabled and all players are ready
    if (session.settings.autoStart && this.areAllPlayersReady(session)) {
      await this.startSession(sessionId);
    }

    return true;
  }

  async leaveSession(sessionId: string, playerId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const playerIndex = session.players.findIndex(p => p.id === playerId);
    if (playerIndex === -1) {
      return false;
    }

    const player = session.players[playerIndex];
    session.players.splice(playerIndex, 1);

    this.emit('playerLeft', { session, player });

    // If host left, transfer host to another player or end session
    if (player.isHost) {
      if (session.players.length > 0) {
        session.players[0].isHost = true;
        session.host = session.players[0];
        this.emit('hostTransferred', { session, newHost: session.host });
      } else {
        await this.endSession(sessionId);
      }
    }

    // Clear current session if this player left
    if (
      this.currentSession?.id === sessionId &&
      this.currentSession.players.some(p => p.id === playerId)
    ) {
      this.currentSession = null;
    }

    return true;
  }

  async startSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.status !== 'waiting') {
      throw new Error('Session cannot be started');
    }

    if (session.players.length < (session.game.requirements?.minPlayers || 1)) {
      throw new Error('Not enough players to start');
    }

    session.status = 'starting';
    session.startedAt = Date.now();

    this.emit('sessionStarting', session);

    // Simulate game launch delay
    setTimeout(() => {
      session.status = 'active';
      this.emit('sessionStarted', session);
    }, 3000);

    return true;
  }

  async endSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.status = 'ended';
    session.endedAt = Date.now();

    // Move to history
    this.sessionHistory.push(session);
    this.activeSessions.delete(sessionId);

    // Clear current session if it's this one
    if (this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }

    this.emit('sessionEnded', session);
    return true;
  }

  async setPlayerReady(sessionId: string, playerId: string, ready: boolean): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const player = session.players.find(p => p.id === playerId);
    if (!player) {
      return false;
    }

    player.isReady = ready;
    this.emit('playerReadyChanged', { session, player, ready });

    // Check if all players are ready and auto-start is enabled
    if (session.settings.autoStart && this.areAllPlayersReady(session)) {
      await this.startSession(sessionId);
    }

    return true;
  }

  async updateSessionSettings(
    sessionId: string,
    settings: Partial<SessionSettings>,
  ): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.settings = { ...session.settings, ...settings };
    this.emit('sessionSettingsUpdated', { session, settings });
    return true;
  }

  async generateInvite(sessionId: string, expirationMinutes: number = 60): Promise<SessionInvite> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const connectionData = JSON.stringify({
      type: 'logaco-session-invite',
      sessionId: session.id,
      hostId: session.host.id,
      connectionInfo: session.connectionInfo,
      timestamp: Date.now(),
    });

    return {
      sessionId: session.id,
      sessionName: session.name,
      hostName: session.host.name,
      gameName: session.game.name,
      playerCount: session.players.length,
      maxPlayers: session.maxPlayers,
      expiresAt: Date.now() + expirationMinutes * 60 * 1000,
      connectionData,
    };
  }

  async joinSessionFromInvite(inviteData: string, player: Player): Promise<boolean> {
    try {
      const invite = JSON.parse(inviteData);

      if (invite.type !== 'logaco-session-invite') {
        throw new Error('Invalid invite format');
      }

      if (Date.now() > invite.expiresAt) {
        throw new Error('Invite has expired');
      }

      return await this.joinSession(invite.sessionId, player);
    } catch (error) {
      throw new Error('Invalid or expired invite');
    }
  }

  getCurrentSession(): GameSession | null {
    return this.currentSession;
  }

  getActiveSessions(): GameSession[] {
    return Array.from(this.activeSessions.values());
  }

  getSessionHistory(): GameSession[] {
    return [...this.sessionHistory].reverse(); // Most recent first
  }

  getSession(sessionId: string): GameSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  private areAllPlayersReady(session: GameSession): boolean {
    return session.players.every(player => player.isReady);
  }

  private generateSessionId(): string {
    return (
      'session_' +
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }

  private generateSecurityToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    );
  }

  // Utility methods for session management
  async kickPlayer(sessionId: string, playerId: string, hostId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const host = session.players.find(p => p.id === hostId && p.isHost);
    if (!host) {
      throw new Error('Only host can kick players');
    }

    return await this.leaveSession(sessionId, playerId);
  }

  async pauseSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== 'active') {
      return false;
    }

    session.status = 'paused';
    this.emit('sessionPaused', session);
    return true;
  }

  async resumeSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== 'paused') {
      return false;
    }

    session.status = 'active';
    this.emit('sessionResumed', session);
    return true;
  }
}

export default SessionManager.getInstance();
