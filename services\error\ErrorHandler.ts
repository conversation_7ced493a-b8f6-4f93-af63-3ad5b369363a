import { SingletonService } from '../base/BaseService';

export interface ErrorReport {
  id: string;
  timestamp: number;
  type: 'service' | 'network' | 'game' | 'ui' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  context?: string;
  stack?: string;
  metadata?: Record<string, any>;
  resolved?: boolean;
  resolvedAt?: number;
  retryCount?: number;
}

export interface RecoveryAction {
  id: string;
  name: string;
  description: string;
  action: () => Promise<boolean>;
  conditions?: (error: ErrorReport) => boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export class ErrorHandler extends SingletonService {
  private errorHistory: ErrorReport[] = [];
  private recoveryActions: Map<string, RecoveryAction> = new Map();
  private maxHistorySize = 100;
  private autoRecoveryEnabled = true;

  constructor() {
    super('ErrorHandler');
    this.setupDefaultRecoveryActions();
  }

  static getInstance(): ErrorHandler {
    return super.getInstance.call(this, 'ErrorHandler');
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('[ErrorHandler] Initializing...');
      
      // Setup global error handlers
      this.setupGlobalErrorHandlers();
      
      this.isInitialized = true;
      this.emitStatusChange('initialized');
      console.log('[ErrorHandler] Initialized successfully');
      return true;
    } catch (error) {
      console.error('[ErrorHandler] Failed to initialize:', error);
      return false;
    }
  }

  /**
   * Report an error and attempt recovery
   */
  async reportError(
    type: ErrorReport['type'],
    severity: ErrorReport['severity'],
    message: string,
    context?: string,
    metadata?: Record<string, any>,
    error?: Error
  ): Promise<string> {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      type,
      severity,
      message,
      context,
      stack: error?.stack,
      metadata,
      resolved: false,
      retryCount: 0,
    };

    // Add to history
    this.addToHistory(errorReport);

    // Emit error event
    this.emit('errorReported', errorReport);

    // Log error
    this.logError(errorReport);

    // Attempt auto-recovery for critical errors
    if (this.autoRecoveryEnabled && (severity === 'critical' || severity === 'high')) {
      await this.attemptRecovery(errorReport);
    }

    return errorReport.id;
  }

  /**
   * Register a recovery action
   */
  registerRecoveryAction(action: RecoveryAction): void {
    this.recoveryActions.set(action.id, action);
    console.log(`[ErrorHandler] Registered recovery action: ${action.name}`);
  }

  /**
   * Attempt to recover from an error
   */
  async attemptRecovery(errorReport: ErrorReport): Promise<boolean> {
    console.log(`[ErrorHandler] Attempting recovery for error: ${errorReport.id}`);

    // Find applicable recovery actions
    const applicableActions = Array.from(this.recoveryActions.values()).filter(
      action => !action.conditions || action.conditions(errorReport)
    );

    for (const action of applicableActions) {
      try {
        // Check retry limits
        if (action.maxRetries && errorReport.retryCount! >= action.maxRetries) {
          continue;
        }

        console.log(`[ErrorHandler] Executing recovery action: ${action.name}`);
        
        // Add delay if specified
        if (action.retryDelay && errorReport.retryCount! > 0) {
          await this.delay(action.retryDelay);
        }

        const success = await action.action();
        
        if (success) {
          errorReport.resolved = true;
          errorReport.resolvedAt = Date.now();
          this.emit('errorResolved', errorReport);
          console.log(`[ErrorHandler] Successfully recovered from error: ${errorReport.id}`);
          return true;
        }
      } catch (recoveryError) {
        console.error(`[ErrorHandler] Recovery action failed:`, recoveryError);
      }

      errorReport.retryCount = (errorReport.retryCount || 0) + 1;
    }

    console.warn(`[ErrorHandler] Failed to recover from error: ${errorReport.id}`);
    return false;
  }

  /**
   * Get error history
   */
  getErrorHistory(type?: ErrorReport['type'], severity?: ErrorReport['severity']): ErrorReport[] {
    let history = [...this.errorHistory];

    if (type) {
      history = history.filter(error => error.type === type);
    }

    if (severity) {
      history = history.filter(error => error.severity === severity);
    }

    return history.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Clear error history
   */
  clearHistory(): void {
    this.errorHistory = [];
    this.emit('historyCleared');
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    resolved: number;
    unresolved: number;
  } {
    const stats = {
      total: this.errorHistory.length,
      byType: {} as Record<string, number>,
      bySeverity: {} as Record<string, number>,
      resolved: 0,
      unresolved: 0,
    };

    this.errorHistory.forEach(error => {
      // Count by type
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      
      // Count by severity
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      
      // Count resolved/unresolved
      if (error.resolved) {
        stats.resolved++;
      } else {
        stats.unresolved++;
      }
    });

    return stats;
  }

  private setupDefaultRecoveryActions(): void {
    // Network reconnection
    this.registerRecoveryAction({
      id: 'network-reconnect',
      name: 'Network Reconnection',
      description: 'Attempt to reconnect network services',
      action: async () => {
        // This would reconnect network services
        console.log('[ErrorHandler] Attempting network reconnection...');
        return true; // Mock success
      },
      conditions: (error) => error.type === 'network',
      maxRetries: 3,
      retryDelay: 2000,
    });

    // Service restart
    this.registerRecoveryAction({
      id: 'service-restart',
      name: 'Service Restart',
      description: 'Restart failed services',
      action: async () => {
        console.log('[ErrorHandler] Attempting service restart...');
        return true; // Mock success
      },
      conditions: (error) => error.type === 'service' && error.severity === 'critical',
      maxRetries: 2,
      retryDelay: 5000,
    });

    // Game session recovery
    this.registerRecoveryAction({
      id: 'game-session-recovery',
      name: 'Game Session Recovery',
      description: 'Attempt to recover game session',
      action: async () => {
        console.log('[ErrorHandler] Attempting game session recovery...');
        return true; // Mock success
      },
      conditions: (error) => error.type === 'game',
      maxRetries: 1,
      retryDelay: 1000,
    });
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof global !== 'undefined') {
      const originalHandler = global.ErrorUtils?.getGlobalHandler();
      
      global.ErrorUtils?.setGlobalHandler((error: Error, isFatal: boolean) => {
        this.reportError(
          'system',
          isFatal ? 'critical' : 'high',
          error.message,
          'Global Error Handler',
          { isFatal },
          error
        );

        // Call original handler if it exists
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }
  }

  private addToHistory(errorReport: ErrorReport): void {
    this.errorHistory.unshift(errorReport);
    
    // Maintain history size limit
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  private logError(errorReport: ErrorReport): void {
    const logLevel = this.getLogLevel(errorReport.severity);
    const message = `[${errorReport.type.toUpperCase()}] ${errorReport.message}`;
    const details = {
      id: errorReport.id,
      context: errorReport.context,
      metadata: errorReport.metadata,
    };

    switch (logLevel) {
      case 'error':
        console.error(message, details);
        break;
      case 'warn':
        console.warn(message, details);
        break;
      default:
        console.log(message, details);
    }
  }

  private getLogLevel(severity: ErrorReport['severity']): 'error' | 'warn' | 'log' {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      default:
        return 'log';
    }
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default ErrorHandler;
