import { Platform, PermissionsAndroid } from 'react-native';
import { SingletonService } from '../base/BaseService';

// Mock BluetoothModule for development - will be replaced with actual implementation
const BluetoothModule = {
  isEnabled: () => Promise.resolve(true),
  requestEnable: () => Promise.resolve(true),
  getBondedDevices: () => Promise.resolve([]),
  startDiscovery: () => Promise.resolve(),
  cancelDiscovery: () => Promise.resolve(),
  connectToDevice: () => Promise.resolve(true),
  disconnectFromDevice: () => Promise.resolve(true),
  writeToDevice: () => Promise.resolve(true),
  onBluetoothEnabled: () => {},
  onBluetoothDisabled: () => {},
  onDeviceDiscovered: () => {},
  onDeviceConnected: () => {},
  onDeviceDisconnected: () => {},
  onDataReceived: () => {},
};

export interface BluetoothDevice {
  id: string;
  name: string;
  address: string;
  paired: boolean;
  connected: boolean;
  rssi?: number;
  deviceClass?: string;
  bondState?: 'none' | 'bonding' | 'bonded';
}

export interface BluetoothServiceInterface {
  isEnabled(): Promise<boolean>;
  requestEnable(): Promise<boolean>;
  requestPermissions(): Promise<boolean>;
  startDiscovery(): Promise<void>;
  stopDiscovery(): Promise<void>;
  getBondedDevices(): Promise<BluetoothDevice[]>;
  getDiscoveredDevices(): Promise<BluetoothDevice[]>;
  connectToDevice(deviceId: string): Promise<boolean>;
  disconnectFromDevice(deviceId: string): Promise<boolean>;
  sendData(deviceId: string, data: string): Promise<boolean>;
  isPaired(deviceId: string): Promise<boolean>;
  pairDevice(deviceId: string): Promise<boolean>;
}

class BluetoothService extends SingletonService implements BluetoothServiceInterface {
  private discoveredDevices: Map<string, BluetoothDevice> = new Map();
  private connectedDevices: Map<string, BluetoothDevice> = new Map();
  private isDiscovering = false;
  private hasPermissions = false;

  // Event callbacks for backward compatibility
  private deviceDiscoveredCallback?: (device: BluetoothDevice) => void;
  private deviceConnectedCallback?: (device: BluetoothDevice) => void;
  private deviceDisconnectedCallback?: (device: BluetoothDevice) => void;
  private dataReceivedCallback?: (deviceId: string, data: string) => void;

  constructor() {
    super('BluetoothService');
  }

  static getInstance(): BluetoothService {
    return super.getInstance.call(this, 'BluetoothService');
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('[BluetoothService] Initializing...');

      // Check if Bluetooth is available on this platform
      if (Platform.OS !== 'android' && Platform.OS !== 'ios') {
        console.warn('[BluetoothService] Bluetooth not supported on this platform');
        return false;
      }

      // Request permissions
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        console.error('[BluetoothService] Failed to get Bluetooth permissions');
        return false;
      }

      // Setup event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      this.emitStatusChange('initialized');
      console.log('[BluetoothService] Initialized successfully');
      return true;
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error(String(error)), 'initialize');
      return false;
    }
  }

  private setupEventListeners(): void {
    try {
      // Set up Bluetooth event listeners
      BluetoothModule.onBluetoothEnabled(() => {
        console.log('[BluetoothService] Bluetooth enabled');
        this.emit('bluetoothEnabled');
      });

      BluetoothModule.onBluetoothDisabled(() => {
        console.log('[BluetoothService] Bluetooth disabled');
        this.connectedDevices.clear();
        this.emit('bluetoothDisabled');
      });

      BluetoothModule.onDeviceDiscovered((device: any) => {
        const bluetoothDevice: BluetoothDevice = {
          id: device.id || device.address,
          name: device.name || 'Unknown Device',
          address: device.address,
          paired: device.bonded || false,
          connected: false,
          rssi: device.rssi,
          deviceClass: device.deviceClass,
          bondState: device.bondState || 'none',
        };

        this.discoveredDevices.set(bluetoothDevice.id, bluetoothDevice);
        this.emit('deviceDiscovered', bluetoothDevice);
        this.deviceDiscoveredCallback?.(bluetoothDevice);
        console.log(`[BluetoothService] Device discovered: ${bluetoothDevice.name}`);
      });

      BluetoothModule.onDeviceConnected((device: any) => {
        const bluetoothDevice: BluetoothDevice = {
          id: device.id || device.address,
          name: device.name || 'Unknown Device',
          address: device.address,
          paired: true,
          connected: true,
          deviceClass: device.deviceClass,
          bondState: 'bonded',
        };

        this.connectedDevices.set(bluetoothDevice.id, bluetoothDevice);
        this.emit('deviceConnected', bluetoothDevice);
        this.deviceConnectedCallback?.(bluetoothDevice);
        console.log(`[BluetoothService] Device connected: ${bluetoothDevice.name}`);
      });

      BluetoothModule.onDeviceDisconnected((device: any) => {
        const deviceId = device.id || device.address;
        const bluetoothDevice = this.connectedDevices.get(deviceId);

        if (bluetoothDevice) {
          bluetoothDevice.connected = false;
          this.connectedDevices.delete(deviceId);
          this.emit('deviceDisconnected', bluetoothDevice);
          this.deviceDisconnectedCallback?.(bluetoothDevice);
          console.log(`[BluetoothService] Device disconnected: ${bluetoothDevice.name}`);
        }
      });

      BluetoothModule.onDataReceived((data: any) => {
        const deviceId = data.device?.id || data.device?.address;
        if (deviceId) {
          this.emit('dataReceived', deviceId, data.data);
          this.dataReceivedCallback?.(deviceId, data.data);
          console.log(`[BluetoothService] Data received from ${deviceId}`);
        }
      });
    } catch (error) {
      this.emitError(
        error instanceof Error ? error : new Error(String(error)),
        'setupEventListeners',
      );
    }
  }

  async isEnabled(): Promise<boolean> {
    try {
      return await BluetoothModule.isEnabled();
    } catch (error) {
      console.error('Error checking Bluetooth status:', error);
      return false;
    }
  }

  async requestEnable(): Promise<boolean> {
    try {
      return await BluetoothModule.requestEnable();
    } catch (error) {
      console.error('Error enabling Bluetooth:', error);
      return false;
    }
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.BLUETOOTH,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADMIN,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ];

        // For Android 12+ (API 31+)
        if (Platform.Version >= 31) {
          permissions.push(
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADVERTISE,
          );
        }

        const granted = await PermissionsAndroid.requestMultiple(permissions);

        return Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED,
        );
      } catch (error) {
        console.error('Error requesting Bluetooth permissions:', error);
        return false;
      }
    }
    return true; // iOS handles permissions automatically
  }

  async startDiscovery(): Promise<void> {
    try {
      if (this.isDiscovering) {
        await this.stopDiscovery();
      }

      this.discoveredDevices.clear();
      await BluetoothModule.startDiscovery();
      this.isDiscovering = true;
    } catch (error) {
      console.error('Error starting Bluetooth discovery:', error);
      throw error;
    }
  }

  async stopDiscovery(): Promise<void> {
    try {
      if (this.isDiscovering) {
        await BluetoothModule.cancelDiscovery();
        this.isDiscovering = false;
      }
    } catch (error) {
      console.error('Error stopping Bluetooth discovery:', error);
      throw error;
    }
  }

  async getBondedDevices(): Promise<BluetoothDevice[]> {
    try {
      const bondedDevices = await BluetoothModule.getBondedDevices();
      return bondedDevices.map((device: any) => ({
        id: device.id,
        name: device.name || 'Unknown Device',
        address: device.address,
        paired: true,
        connected: this.connectedDevices.has(device.id),
      }));
    } catch (error) {
      console.error('Error getting bonded devices:', error);
      return [];
    }
  }

  async getDiscoveredDevices(): Promise<BluetoothDevice[]> {
    return Array.from(this.discoveredDevices.values());
  }

  async connectToDevice(deviceId: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.connectToDevice(deviceId);
      return success;
    } catch (error) {
      console.error('Error connecting to device:', error);
      return false;
    }
  }

  async disconnectFromDevice(deviceId: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.disconnectFromDevice(deviceId);
      if (success) {
        this.connectedDevices.delete(deviceId);
      }
      return success;
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      return false;
    }
  }

  async sendData(deviceId: string, data: string): Promise<boolean> {
    try {
      const success = await BluetoothModule.writeToDevice(deviceId, data);
      return success;
    } catch (error) {
      console.error('Error sending data to device:', error);
      return false;
    }
  }

  onDeviceDiscovered(callback: (device: BluetoothDevice) => void): void {
    this.deviceDiscoveredCallback = callback;
  }

  onDeviceConnected(callback: (device: BluetoothDevice) => void): void {
    this.deviceConnectedCallback = callback;
  }

  onDeviceDisconnected(callback: (device: BluetoothDevice) => void): void {
    this.deviceDisconnectedCallback = callback;
  }

  onDataReceived(callback: (deviceId: string, data: string) => void): void {
    this.dataReceivedCallback = callback;
  }

  async isPaired(deviceId: string): Promise<boolean> {
    try {
      const bondedDevices = await this.getBondedDevices();
      return bondedDevices.some(device => device.id === deviceId);
    } catch (error) {
      console.error('[BluetoothService] Error checking if device is paired:', error);
      return false;
    }
  }

  async pairDevice(deviceId: string): Promise<boolean> {
    try {
      // This would require native implementation
      console.log(`[BluetoothService] Attempting to pair with device: ${deviceId}`);
      // Mock implementation for now - would need native module
      return true;
    } catch (error) {
      console.error('[BluetoothService] Error pairing device:', error);
      return false;
    }
  }
}

export default BluetoothService;
