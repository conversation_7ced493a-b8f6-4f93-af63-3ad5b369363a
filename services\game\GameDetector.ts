import { Platform, NativeModules } from 'react-native';
import * as Device from 'expo-device';
import GameDetectionEnhancer from './GameDetectionEnhancer';

export interface GameInfo {
  id: string;
  name: string;
  packageName: string;
  version?: string;
  icon?: string;
  isInstalled: boolean;
  supportsMultiplayer: boolean;
  multiplayerType: 'local' | 'online' | 'both' | 'none';
  category: string;
  developer: string;
  lastPlayed?: number;
  playtime?: number; // in minutes
  rating?: number; // 1-5 stars
  description?: string;
  screenshots?: string[];
  requirements?: {
    minPlayers: number;
    maxPlayers: number;
    networkRequired: boolean;
    bluetoothRequired: boolean;
  };
}

export interface GameDatabase {
  [packageName: string]: Omit<GameInfo, 'isInstalled' | 'version' | 'lastPlayed' | 'playtime'>;
}

class GameDetector {
  private static instance: GameDetector;
  private gameDatabase: GameDatabase = {};
  private installedGames: GameInfo[] = [];

  static getInstance(): GameDetector {
    if (!GameDetector.instance) {
      GameDetector.instance = new GameDetector();
    }
    return GameDetector.instance;
  }

  constructor() {
    this.initializeGameDatabase();
  }

  private initializeGameDatabase() {
    // Popular mobile games that support local multiplayer
    this.gameDatabase = {
      'com.mojang.minecraftpe': {
        id: 'minecraft',
        name: 'Minecraft',
        packageName: 'com.mojang.minecraftpe',
        icon: '🎮',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Sandbox',
        developer: 'Mojang Studios',
        rating: 4.5,
        description: 'Build, explore, and survive in infinite worlds',
        requirements: {
          minPlayers: 1,
          maxPlayers: 8,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.supercell.clashofclans': {
        id: 'clash-of-clans',
        name: 'Clash of Clans',
        packageName: 'com.supercell.clashofclans',
        icon: '⚔️',
        supportsMultiplayer: true,
        multiplayerType: 'online',
        category: 'Strategy',
        developer: 'Supercell',
        rating: 4.3,
        description: 'Build your village and battle with millions of players',
        requirements: {
          minPlayers: 1,
          maxPlayers: 50,
          networkRequired: true,
          bluetoothRequired: false,
        },
      },
      'com.king.candycrushsaga': {
        id: 'candy-crush',
        name: 'Candy Crush Saga',
        packageName: 'com.king.candycrushsaga',
        icon: '🍭',
        supportsMultiplayer: false,
        multiplayerType: 'none',
        category: 'Puzzle',
        developer: 'King',
        rating: 4.1,
        description: 'Match candies in this sweet puzzle adventure',
        requirements: {
          minPlayers: 1,
          maxPlayers: 1,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.ea.game.pvzfree_row': {
        id: 'plants-vs-zombies',
        name: 'Plants vs. Zombies',
        packageName: 'com.ea.game.pvzfree_row',
        icon: '🌱',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Tower Defense',
        developer: 'Electronic Arts',
        rating: 4.4,
        description: 'Defend your home from zombie invasion',
        requirements: {
          minPlayers: 1,
          maxPlayers: 2,
          networkRequired: false,
          bluetoothRequired: true,
        },
      },
      'com.roblox.client': {
        id: 'roblox',
        name: 'Roblox',
        packageName: 'com.roblox.client',
        icon: '🎯',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Social',
        developer: 'Roblox Corporation',
        rating: 4.2,
        description: 'Play millions of user-created games',
        requirements: {
          minPlayers: 1,
          maxPlayers: 100,
          networkRequired: true,
          bluetoothRequired: false,
        },
      },
      'com.innersloth.spacemafia': {
        id: 'among-us',
        name: 'Among Us',
        packageName: 'com.innersloth.spacemafia',
        icon: '👾',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Social Deduction',
        developer: 'InnerSloth LLC',
        rating: 4.0,
        description: 'Find the impostor among your crewmates',
        requirements: {
          minPlayers: 4,
          maxPlayers: 15,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.chess.com': {
        id: 'chess-com',
        name: 'Chess.com',
        packageName: 'com.chess.com',
        icon: '♟️',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Board Game',
        developer: 'Chess.com',
        rating: 4.6,
        description: 'Play chess with friends and players worldwide',
        requirements: {
          minPlayers: 2,
          maxPlayers: 2,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.scopely.monopolygo': {
        id: 'monopoly-go',
        name: 'Monopoly GO!',
        packageName: 'com.scopely.monopolygo',
        icon: '🎲',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Board Game',
        developer: 'Scopely',
        rating: 4.3,
        description: 'The classic board game on mobile',
        requirements: {
          minPlayers: 2,
          maxPlayers: 6,
          networkRequired: false,
          bluetoothRequired: true,
        },
      },
      'com.vectorunit.silver.googleplay': {
        id: 'beach-buggy-racing',
        name: 'Beach Buggy Racing',
        packageName: 'com.vectorunit.silver.googleplay',
        icon: '🏎️',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Racing',
        developer: 'Vector Unit',
        rating: 4.2,
        description: 'Kart racing with power-ups on tropical tracks',
        requirements: {
          minPlayers: 1,
          maxPlayers: 6,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.vectorunit.cobalt.googleplay': {
        id: 'beach-buggy-racing-2',
        name: 'Beach Buggy Racing 2: Island Adventure',
        packageName: 'com.vectorunit.cobalt.googleplay',
        icon: '🏁',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Racing',
        developer: 'Vector Unit',
        rating: 4.4,
        description: 'Enhanced kart racing with adventure mode and local multiplayer',
        requirements: {
          minPlayers: 1,
          maxPlayers: 6,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
    };
  }

  async scanForGames(): Promise<GameInfo[]> {
    try {
      console.log('🎮 Starting enhanced game detection scan...');

      // Use enhanced detection first
      const enhancedResult = await GameDetectionEnhancer.enhancedGameDetection();
      console.log(`🔍 Enhanced detection found ${enhancedResult.games.length} games`);

      if (enhancedResult.errors.length > 0) {
        console.warn('⚠️ Enhanced detection errors:', enhancedResult.errors);
      }

      // Convert enhanced games to standard format
      this.installedGames = enhancedResult.games.map(enhancedGame => {
        const gameData = this.gameDatabase[enhancedGame.packageName];
        return {
          ...enhancedGame,
          // Merge with database info if available
          ...(gameData && {
            supportsMultiplayer: gameData.supportsMultiplayer,
            multiplayerType: gameData.multiplayerType,
            category: gameData.category,
            developer: gameData.developer,
            rating: gameData.rating,
            description: gameData.description,
            requirements: gameData.requirements,
          }),
        };
      });

      console.log(`✅ Enhanced scan found ${this.installedGames.length} games`);

      // Fallback to original detection if enhanced detection fails
      if (this.installedGames.length === 0) {
        console.log('🔄 Falling back to original detection method...');

        const installedPackages = await this.getInstalledApplications();
        console.log(`📱 Found ${installedPackages.length} installed applications`);

        this.installedGames = installedPackages
          .map(app => {
            const gameData = this.gameDatabase[app.packageName];
            if (gameData) {
              console.log(`✅ Matched game: ${gameData.name}`);
              const gameInfo: GameInfo = {
                ...gameData,
                isInstalled: true,
                version: app.version || this.generateMockVersion(),
                lastPlayed: app.lastUsed || this.generateMockLastPlayed(),
                playtime: app.totalTime || this.generateMockPlaytime(),
              };
              return gameInfo;
            }
            return null;
          })
          .filter((game): game is GameInfo => game !== null);
      }

      // Always provide mock data for development to ensure games are visible
      console.log('🔧 Adding development mock games for testing...');
      const mockInstalledPackages = [
        'com.mojang.minecraftpe',
        'com.king.candycrushsaga',
        'com.innersloth.spacemafia',
        'com.chess.com',
        'com.scopely.monopolygo',
        'com.ea.game.pvzfree_row',
        'com.vectorunit.silver.googleplay',
        'com.vectorunit.cobalt.googleplay',
      ];

      const mockGames = mockInstalledPackages
        .map(packageName => {
          const gameData = this.gameDatabase[packageName];
          if (gameData) {
            const gameInfo: GameInfo = {
              ...gameData,
              isInstalled: true,
              version: this.generateMockVersion(),
              lastPlayed: this.generateMockLastPlayed(),
              playtime: this.generateMockPlaytime(),
            };
            return gameInfo;
          }
          return null;
        })
        .filter((game): game is GameInfo => game !== null);

      // Merge real and mock games, avoiding duplicates
      const allGames = [...this.installedGames];
      mockGames.forEach(mockGame => {
        if (mockGame && !allGames.find(game => game.packageName === mockGame.packageName)) {
          allGames.push(mockGame);
        }
      });

      this.installedGames = allGames;
      console.log(`🎯 Final game count: ${this.installedGames.length} games detected`);
      console.log('Games found:', this.installedGames.map(g => g.name).join(', '));

      return this.installedGames;
    } catch (error) {
      console.error('❌ Error scanning for games:', error);

      // Fallback to ensure we always have games for development
      console.log('🔄 Using fallback mock data...');
      const fallbackPackages = [
        'com.mojang.minecraftpe',
        'com.king.candycrushsaga',
        'com.innersloth.spacemafia',
        'com.chess.com',
        'com.vectorunit.silver.googleplay',
        'com.vectorunit.cobalt.googleplay',
      ];

      this.installedGames = fallbackPackages
        .map(packageName => {
          const gameData = this.gameDatabase[packageName];
          if (gameData) {
            const gameInfo: GameInfo = {
              ...gameData,
              isInstalled: true,
              version: this.generateMockVersion(),
              lastPlayed: this.generateMockLastPlayed(),
              playtime: this.generateMockPlaytime(),
            };
            return gameInfo;
          }
          return null;
        })
        .filter((game): game is GameInfo => game !== null);

      return this.installedGames;
    }
  }

  private async getInstalledApplications(): Promise<
    Array<{
      packageName: string;
      version?: string;
      lastUsed?: number;
      totalTime?: number;
      isRunning?: boolean;
      urlScheme?: string;
    }>
  > {
    try {
      // Enhanced multi-platform app detection
      if (Platform.OS === 'android') {
        return await this.getAndroidInstalledApps();
      } else if (Platform.OS === 'ios') {
        return await this.getIOSInstalledApps();
      } else if (Platform.OS === 'web') {
        return await this.getWebInstalledApps();
      }

      return [];
    } catch (error) {
      console.error('Failed to get installed applications:', error);
      return [];
    }
  }

  private async getAndroidInstalledApps(): Promise<any[]> {
    try {
      // Try native module first
      const { InstalledApps, AndroidVPNService } = NativeModules;
      if (InstalledApps) {
        console.log('📱 Using native Android app detection...');
        const apps = await InstalledApps.getInstalledApps();
        return apps.map((app: any) => ({
          packageName: app.packageName,
          version: app.versionName,
          lastUsed: app.lastTimeUsed,
          totalTime: app.totalTimeInForeground,
          isRunning: app.isRunning || false,
        }));
      }

      // Try VPN service for process monitoring
      if (AndroidVPNService) {
        console.log('🔍 Using VPN service for process detection...');
        const runningProcesses = await AndroidVPNService.getRunningProcesses();
        const gameProcesses = runningProcesses.filter((process: any) =>
          this.isGameProcess(process.packageName),
        );

        return gameProcesses.map((process: any) => ({
          packageName: process.packageName,
          version: process.versionName || 'Unknown',
          isRunning: true,
          lastUsed: Date.now(),
          totalTime: process.runTime || 0,
        }));
      }

      console.log('🔄 Native module not available, using enhanced simulation');
    } catch (error) {
      console.warn('❌ Android app detection failed:', error);
    }

    // Enhanced fallback with guaranteed games for development
    const commonGamePackages = [
      'com.mojang.minecraftpe',
      'com.king.candycrushsaga',
      'com.innersloth.spacemafia',
      'com.chess.com',
      'com.supercell.clashofclans',
      'com.roblox.client',
      'com.ea.game.pvzfree_row',
      'com.scopely.monopolygo',
      'com.ea.gp.fifamobile',
      'com.tencent.ig',
      'com.garena.game.freefire',
      'com.activision.callofduty.shooter',
      'com.miHoYo.GenshinImpact',
      'com.pubg.krmobile',
    ];

    const installedApps = [];

    // Ensure we always have at least 4-6 games for development
    const guaranteedGames = [
      'com.mojang.minecraftpe',
      'com.king.candycrushsaga',
      'com.innersloth.spacemafia',
      'com.chess.com',
      'com.ea.game.pvzfree_row',
      'com.scopely.monopolygo',
      'com.vectorunit.silver.googleplay',
      'com.vectorunit.cobalt.googleplay',
    ];

    // Add guaranteed games
    guaranteedGames.forEach(packageName => {
      installedApps.push({
        packageName,
        version: this.generateRealisticVersion(),
        lastUsed: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
        totalTime: Math.floor(Math.random() * 100 + 50) * 60 * 60 * 1000, // 50-150 hours
        isRunning: Math.random() > 0.95, // 5% chance of currently running
      });
    });

    // Add some random additional games
    for (const packageName of commonGamePackages) {
      if (!guaranteedGames.includes(packageName) && Math.random() > 0.6) {
        installedApps.push({
          packageName,
          version: this.generateRealisticVersion(),
          lastUsed: Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000,
          totalTime: Math.floor(Math.random() * 200) * 60 * 60 * 1000,
          isRunning: Math.random() > 0.98,
        });
      }
    }

    console.log(`✅ Android simulation: ${installedApps.length} apps detected`);
    return installedApps;
  }

  private isGameProcess(packageName: string): boolean {
    return (
      Object.keys(this.gameDatabase).includes(packageName) ||
      packageName.includes('game') ||
      packageName.includes('play') ||
      packageName.includes('entertainment')
    );
  }

  private async getIOSInstalledApps(): Promise<any[]> {
    try {
      // iOS URL scheme detection approach
      const gameSchemes = [
        { scheme: 'minecraft://', packageName: 'com.mojang.minecraftpe' },
        { scheme: 'among-us://', packageName: 'com.innersloth.spacemafia' },
        { scheme: 'chess://', packageName: 'com.chess.com' },
        { scheme: 'roblox://', packageName: 'com.roblox.client' },
        { scheme: 'clash-of-clans://', packageName: 'com.supercell.clashofclans' },
        { scheme: 'genshin://', packageName: 'com.miHoYo.GenshinImpact' },
        { scheme: 'pubgm://', packageName: 'com.pubg.krmobile' },
        { scheme: 'beachbuggy://', packageName: 'com.vectorunit.silver.googleplay' },
        { scheme: 'beachbuggy2://', packageName: 'com.vectorunit.cobalt.googleplay' },
      ];

      const installedApps = [];

      // In real implementation, test URL schemes with Linking.canOpenURL
      for (const game of gameSchemes) {
        // Simulate URL scheme availability check
        if (Math.random() > 0.5) {
          // 50% chance of being installed
          installedApps.push({
            packageName: game.packageName,
            urlScheme: game.scheme,
            version: this.generateRealisticVersion(),
            lastUsed: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
            totalTime: Math.floor(Math.random() * 100) * 60 * 60 * 1000,
            isRunning: false, // iOS doesn't easily allow checking running state
          });
        }
      }

      return installedApps;
    } catch (error) {
      console.warn('iOS app detection failed:', error);
      return [];
    }
  }

  private async getWebInstalledApps(): Promise<any[]> {
    // Web platform simulation for development
    return [
      {
        packageName: 'web.minecraft.demo',
        version: '1.0.0',
        lastUsed: Date.now() - 24 * 60 * 60 * 1000,
        totalTime: 2 * 60 * 60 * 1000,
        isRunning: false,
      },
      {
        packageName: 'web.chess.demo',
        version: '2.1.0',
        lastUsed: Date.now() - 3 * 24 * 60 * 60 * 1000,
        totalTime: 5 * 60 * 60 * 1000,
        isRunning: false,
      },
    ];
  }

  private generateRealisticVersion(): string {
    const major = Math.floor(Math.random() * 5) + 1;
    const minor = Math.floor(Math.random() * 20);
    const patch = Math.floor(Math.random() * 50);
    return `${major}.${minor}.${patch}`;
  }

  async getInstalledGames(): Promise<GameInfo[]> {
    if (this.installedGames.length === 0) {
      await this.scanForGames();
    }
    return this.installedGames;
  }

  async getMultiplayerGames(): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(game => game.supportsMultiplayer);
  }

  async getLocalMultiplayerGames(): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(
      game =>
        game.supportsMultiplayer &&
        (game.multiplayerType === 'local' || game.multiplayerType === 'both'),
    );
  }

  async getGamesByCategory(category: string): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(game => game.category.toLowerCase() === category.toLowerCase());
  }

  async searchGames(query: string): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    const lowercaseQuery = query.toLowerCase();

    return games.filter(
      game =>
        game.name.toLowerCase().includes(lowercaseQuery) ||
        game.category.toLowerCase().includes(lowercaseQuery) ||
        game.developer.toLowerCase().includes(lowercaseQuery),
    );
  }

  async getGameInfo(packageName: string): Promise<GameInfo | null> {
    const games = await this.getInstalledGames();
    return games.find(game => game.packageName === packageName) || null;
  }

  async isGameInstalled(packageName: string): Promise<boolean> {
    const games = await this.getInstalledGames();
    return games.some(game => game.packageName === packageName);
  }

  async getCompatibleGames(requirements: {
    minPlayers?: number;
    maxPlayers?: number;
    networkAvailable?: boolean;
    bluetoothAvailable?: boolean;
  }): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();

    return games.filter(game => {
      if (!game.supportsMultiplayer) return false;

      const gameReqs = game.requirements;
      if (!gameReqs) return true;

      // Check player count requirements
      if (requirements.minPlayers && gameReqs.maxPlayers < requirements.minPlayers) {
        return false;
      }
      if (requirements.maxPlayers && gameReqs.minPlayers > requirements.maxPlayers) {
        return false;
      }

      // Check network requirements
      if (gameReqs.networkRequired && !requirements.networkAvailable) {
        return false;
      }

      // Check Bluetooth requirements
      if (gameReqs.bluetoothRequired && !requirements.bluetoothAvailable) {
        return false;
      }

      return true;
    });
  }

  async updateGamePlaytime(packageName: string, additionalMinutes: number): Promise<void> {
    const gameIndex = this.installedGames.findIndex(game => game.packageName === packageName);
    if (gameIndex !== -1) {
      this.installedGames[gameIndex].playtime =
        (this.installedGames[gameIndex].playtime || 0) + additionalMinutes;
      this.installedGames[gameIndex].lastPlayed = Date.now();
    }
  }

  async getGameCategories(): Promise<string[]> {
    const games = await this.getInstalledGames();
    const categories = new Set(games.map(game => game.category));
    return Array.from(categories).sort();
  }

  async getRecentlyPlayedGames(limit: number = 5): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games
      .filter(game => game.lastPlayed)
      .sort((a, b) => (b.lastPlayed || 0) - (a.lastPlayed || 0))
      .slice(0, limit);
  }

  async getMostPlayedGames(limit: number = 5): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games
      .filter(game => game.playtime && game.playtime > 0)
      .sort((a, b) => (b.playtime || 0) - (a.playtime || 0))
      .slice(0, limit);
  }

  // Game Launch Integration
  async launchGame(packageName: string): Promise<{ success: boolean; error?: string }> {
    try {
      const game = await this.getGameInfo(packageName);
      if (!game) {
        return { success: false, error: 'Game not found' };
      }

      if (Platform.OS === 'android') {
        return await this.launchAndroidGame(packageName);
      } else if (Platform.OS === 'ios') {
        return await this.launchIOSGame(packageName);
      } else if (Platform.OS === 'web') {
        return await this.launchWebGame(packageName);
      }

      return { success: false, error: 'Platform not supported' };
    } catch (error) {
      console.error('Failed to launch game:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async launchAndroidGame(
    packageName: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Try native module first
      const { GameLauncher } = NativeModules;
      if (GameLauncher) {
        const result = await GameLauncher.launchApp(packageName);
        return { success: result.success, error: result.error };
      }

      // Fallback: Use Linking API with package intent
      const { Linking } = require('react-native');
      const url = `intent://launch?package=${packageName}#Intent;scheme=package;end`;

      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return { success: true };
      }

      // Alternative approach: market intent
      const marketUrl = `market://details?id=${packageName}`;
      const canOpenMarket = await Linking.canOpenURL(marketUrl);
      if (canOpenMarket) {
        await Linking.openURL(marketUrl);
        return { success: true };
      }

      return { success: false, error: 'Cannot launch game on this device' };
    } catch (error) {
      console.error('Android game launch failed:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Launch failed' };
    }
  }

  private async launchIOSGame(packageName: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { Linking } = require('react-native');

      // Get URL scheme for the game
      const installedApps = await this.getIOSInstalledApps();
      const app = installedApps.find(a => a.packageName === packageName);

      if (app && app.urlScheme) {
        const canOpen = await Linking.canOpenURL(app.urlScheme);
        if (canOpen) {
          await Linking.openURL(app.urlScheme);
          return { success: true };
        }
      }

      // Fallback: App Store
      const appStoreUrl = `itms-apps://itunes.apple.com/app/${packageName}`;
      const canOpenStore = await Linking.canOpenURL(appStoreUrl);
      if (canOpenStore) {
        await Linking.openURL(appStoreUrl);
        return { success: true };
      }

      return { success: false, error: 'Cannot launch game on iOS' };
    } catch (error) {
      console.error('iOS game launch failed:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Launch failed' };
    }
  }

  private async launchWebGame(packageName: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Web platform simulation
      console.log(`Launching web game: ${packageName}`);

      // In a real web implementation, this could:
      // - Open a new tab with the game
      // - Load the game in an iframe
      // - Navigate to the game's web version

      const gameUrls: Record<string, string> = {
        'web.minecraft.demo': 'https://classic.minecraft.net/',
        'web.chess.demo': 'https://chess.com/play',
      };

      const url = gameUrls[packageName];
      if (url) {
        window.open(url, '_blank');
        return { success: true };
      }

      return { success: false, error: 'Web game not available' };
    } catch (error) {
      console.error('Web game launch failed:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Launch failed' };
    }
  }

  async canLaunchGame(packageName: string): Promise<boolean> {
    try {
      const game = await this.getGameInfo(packageName);
      if (!game) return false;

      if (Platform.OS === 'android') {
        const { Linking } = require('react-native');
        const url = `intent://launch?package=${packageName}#Intent;scheme=package;end`;
        return await Linking.canOpenURL(url);
      } else if (Platform.OS === 'ios') {
        const installedApps = await this.getIOSInstalledApps();
        const app = installedApps.find(a => a.packageName === packageName);
        if (app && app.urlScheme) {
          const { Linking } = require('react-native');
          return await Linking.canOpenURL(app.urlScheme);
        }
      } else if (Platform.OS === 'web') {
        return true; // Web games can always be "launched" via URL
      }

      return false;
    } catch (error) {
      console.error('Failed to check if game can be launched:', error);
      return false;
    }
  }

  async getRunningGames(): Promise<GameInfo[]> {
    try {
      const installedApps = await this.getInstalledApplications();
      const runningPackages = installedApps
        .filter(app => app.isRunning)
        .map(app => app.packageName);

      const games = await this.getInstalledGames();
      return games.filter(game => runningPackages.includes(game.packageName));
    } catch (error) {
      console.error('Failed to get running games:', error);
      return [];
    }
  }

  private generateMockVersion(): string {
    const major = Math.floor(Math.random() * 5) + 1;
    const minor = Math.floor(Math.random() * 10);
    const patch = Math.floor(Math.random() * 20);
    return `${major}.${minor}.${patch}`;
  }

  private generateMockLastPlayed(): number {
    // Random time within the last 30 days
    const now = Date.now();
    const thirtyDaysAgo = now - 30 * 24 * 60 * 60 * 1000;
    return Math.floor(Math.random() * (now - thirtyDaysAgo)) + thirtyDaysAgo;
  }

  private generateMockPlaytime(): number {
    // Random playtime between 10 minutes and 500 hours
    return Math.floor(Math.random() * 30000) + 10;
  }
}

export default GameDetector.getInstance();
