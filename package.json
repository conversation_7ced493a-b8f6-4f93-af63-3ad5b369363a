{"name": "logaco", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --format json --output-file eslint-results.json", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "build": "expo export", "build:web": "expo export --platform web", "build:android": "expo export --platform android", "build:ios": "expo export --platform ios", "test:smoke": "jest --testPathPattern=smoke --passWithNoTests", "test:unit": "jest --testPathPattern=__tests__/services", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:native": "jest --testPath<PERSON>attern=native", "test:networking": "jest --testPathPattern=networking", "test:database": "jest --testPathPattern=database", "analyze": "npx expo export --platform web && npx bundlesize", "clean": "expo r -c", "postinstall": "expo install --fix"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@reduxjs/toolkit": "^2.8.2", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "expo": "^53.0.20", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-network": "^7.1.5", "expo-notifications": "^0.31.4", "expo-router": "~5.1.4", "expo-splash-screen": "^0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-store-review": "~8.1.5", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.5", "react-native-ble-plx": "^3.5.0", "react-native-bluetooth-classic": "^1.73.0-rc.14", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "react-native-wifi-p2p": "^3.6.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "bundlesize": "^0.18.1", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "prettier": "^3.2.5", "ts-jest": "^29.3.4", "typescript": "~5.8.3"}, "bundlesize": [{"path": "dist/static/js/*.js", "maxSize": "2MB"}, {"path": "dist/static/css/*.css", "maxSize": "500KB"}], "private": true}